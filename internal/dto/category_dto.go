package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type CategoryResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func FromDomainCategory(category domain.Category) CategoryResponse {
	return CategoryResponse{
		ID:        category.ID,
		Name:      category.Name,
		CreatedAt: category.CreatedAt,
		UpdatedAt: category.UpdatedAt,
	}
}

type CategoryListResponse struct {
	Results []CategoryResponse `json:"results"`
	Total   int                `json:"total"`
	Message string             `json:"message"`
}

func NewCategoryListResponse(categories []*domain.Category, message string) CategoryListResponse {
	categoriesResponse := make([]CategoryResponse, len(categories))
	for i, cat := range categories {
		categoriesResponse[i] = FromDomainCategory(*cat)
	}
	return CategoryListResponse{
		Results: categoriesResponse,
		Total:   len(categories),
		Message: message,
	}
}

type CreateCategoryRequest struct {
	Name string `json:"name" binding:"required"`
}

func ToDomainCategory(r *CreateCategoryRequest) *domain.Category {
	return &domain.Category{
		Name: r.Name,
	}
}

// AssignmentCategory DTOs
type AssignmentCategoryResponse struct {
	AssignmentID    uint             `json:"assignmentId"`
	CategoryID      uint             `json:"categoryId"`
	PresentValue    float64          `json:"presentValue"`
	AsAnalyzedValue float64          `json:"asAnalyzedValue"`
	CreatedAt       time.Time        `json:"createdAt"`
	UpdatedAt       time.Time        `json:"updatedAt"`
	Category        CategoryResponse `json:"category,omitempty"`
}

func FromDomainAssignmentCategory(assignmentCategory domain.AssignmentCategory) AssignmentCategoryResponse {
	response := AssignmentCategoryResponse{
		AssignmentID:    assignmentCategory.AssignmentID,
		CategoryID:      assignmentCategory.CategoryID,
		PresentValue:    assignmentCategory.PresentValue,
		AsAnalyzedValue: assignmentCategory.AsAnalyzedValue,
		CreatedAt:       assignmentCategory.CreatedAt,
		UpdatedAt:       assignmentCategory.UpdatedAt,
	}

	if assignmentCategory.Category.ID != 0 {
		response.Category = FromDomainCategory(assignmentCategory.Category)
	}

	return response
}

type AssignmentCategoryRequest struct {
	CategoryID      uint    `json:"categoryId" binding:"required"`
	PresentValue    float64 `json:"presentValue" binding:"required"`
	AsAnalyzedValue float64 `json:"asAnalyzedValue" binding:"required"`
}
