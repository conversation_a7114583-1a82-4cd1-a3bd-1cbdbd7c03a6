package dto

import (
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type AssignmentResponse struct {
	ID               uint                    `json:"id"`
	Status           domain.AssignmentStatus `json:"status"`
	CreatedAt        time.Time               `json:"createdAt"`
	UpdatedAt        time.Time               `json:"updatedAt"`
	CreatedByID      uint                    `json:"createdById"`
	AssignmentLeadID uint                    `json:"assignmentLeadId"`

	// Assignment Information
	ClaimNumber        string    `json:"claimNumber"`
	InsuredName        string    `json:"insuredName"`
	AssignmentName     string    `json:"assignmentName"`
	DateOfLoss         time.Time `json:"dateOfLoss"`
	PrimaryContactID   uint      `json:"primaryContactId"`
	SecondaryContactID *uint     `json:"secondaryContactId"`

	// Location Information
	LossLocationID uint `json:"lossLocationId"`

	// Detailed Information
	ClaimDetail           *string `json:"claimDetail"`
	AssignmentDescription *string `json:"assignmentDescription"`

	// Foreign key IDs
	AdjusterID uint `json:"adjusterId"`

	// Nested response DTOs for related entities
	CreatedBy        *UserResponse                `json:"createdBy,omitempty"`
	AssignmentLead   *ConsultantResponse          `json:"assignmentLead,omitempty"`
	Adjuster         *AdjusterResponse            `json:"adjuster,omitempty"`
	Carriers         []CarrierResponse            `json:"carriers,omitempty"`
	CategoryData     []AssignmentCategoryResponse `json:"categoryData,omitempty"`
	PrimaryContact   *ContactResponse             `json:"primaryContact,omitempty"`
	SecondaryContact *ContactResponse             `json:"secondaryContact,omitempty"`
	LossLocation     *LocationResponse            `json:"lossLocation,omitempty"`
}

func FromDomainAssignment(assignment domain.Assignment) AssignmentResponse {
	response := AssignmentResponse{
		ID:                    assignment.ID,
		Status:                assignment.Status,
		CreatedAt:             assignment.CreatedAt,
		UpdatedAt:             assignment.UpdatedAt,
		CreatedByID:           assignment.CreatedByID,
		AssignmentLeadID:      assignment.AssignmentLeadID,
		ClaimNumber:           assignment.ClaimNumber,
		InsuredName:           assignment.InsuredName,
		AssignmentName:        assignment.AssignmentName,
		DateOfLoss:            assignment.DateOfLoss,
		PrimaryContactID:      assignment.PrimaryContactID,
		SecondaryContactID:    assignment.SecondaryContactID,
		LossLocationID:        assignment.LossLocationID,
		ClaimDetail:           assignment.ClaimDetail,
		AssignmentDescription: assignment.AssignmentDescription,
		AdjusterID:            assignment.AdjusterID,
	}

	if assignment.CreatedBy.ID != 0 {
		user := FromDomainUser(assignment.CreatedBy)
		response.CreatedBy = &user
	}

	if assignment.AssignmentLead.ID != 0 {
		consultant := FromDomainConsultant(assignment.AssignmentLead)
		response.AssignmentLead = &consultant
	}

	if assignment.Adjuster.ID != 0 {
		adjuster := FromDomainAdjuster(assignment.Adjuster)
		response.Adjuster = &adjuster
	}

	if assignment.PrimaryContact.ID != 0 {
		contact := FromDomainContact(assignment.PrimaryContact)
		response.PrimaryContact = &contact
	}

	if assignment.SecondaryContact != nil && assignment.SecondaryContact.ID != 0 {
		contact := FromDomainContact(*assignment.SecondaryContact)
		response.SecondaryContact = &contact
	}

	if assignment.LossLocation.ID != 0 {
		location := FromDomainLocation(assignment.LossLocation)
		response.LossLocation = &location
	}

	if len(assignment.Carriers) > 0 {
		response.Carriers = make([]CarrierResponse, len(assignment.Carriers))
		for i, carrier := range assignment.Carriers {
			response.Carriers[i] = FromDomainCarrier(*carrier)
		}
	}

	if len(assignment.CategoryData) > 0 {
		response.CategoryData = make([]AssignmentCategoryResponse, len(assignment.CategoryData))
		for i, categoryData := range assignment.CategoryData {
			response.CategoryData[i] = FromDomainAssignmentCategory(categoryData)
		}
	}

	return response
}

type AssignmentsListResponse struct {
	Results []AssignmentResponse `json:"results"`
	Total   int                  `json:"total"`
	Message string               `json:"message"`
}

func NewAssignmentsListResponse(assignments []*domain.Assignment, message string) AssignmentsListResponse {
	assignmentResponses := make([]AssignmentResponse, len(assignments))
	for i, assignment := range assignments {
		assignmentResponses[i] = FromDomainAssignment(*assignment)
	}
	return AssignmentsListResponse{
		Results: assignmentResponses,
		Total:   len(assignments),
		Message: message,
	}
}

type CreateAssignmentRequest struct {
	Status                string                      `json:"status"`
	CreatedByID           uint                        `json:"createdById"`
	AssignmentLeadID      uint                        `json:"assignmentLeadId" binding:"required"`
	AdjusterID            uint                        `json:"adjusterId" binding:"required"`
	Carriers              []uint                      `json:"carriers"`
	CategoryData          []AssignmentCategoryRequest `json:"categoryData"`
	ClaimNumber           string                      `json:"claimNumber" binding:"required"`
	InsuredName           string                      `json:"insuredName" binding:"required"`
	AssignmentName        string                      `json:"assignmentName" binding:"required"`
	DateOfLoss            time.Time                   `json:"dateOfLoss" binding:"required"`
	PrimaryContactID      uint                        `json:"primaryContactId" binding:"required"`
	SecondaryContactID    *uint                       `json:"secondaryContactId"`
	LossLocationID        uint                        `json:"lossLocationId" binding:"required"`
	ClaimDetail           *string                     `json:"claimDetail"`
	AssignmentDescription *string                     `json:"assignmentDescription"`
}

func ToDomainAssignment(r *CreateAssignmentRequest, createdByID uint) *domain.Assignment {
	var cars []*domain.Carrier
	for _, carrID := range r.Carriers {
		cars = append(cars, &domain.Carrier{ID: carrID})
	}

	assignment := &domain.Assignment{
		Status:                domain.AssignmentStatus(r.Status),
		CreatedByID:           createdByID,
		AssignmentLeadID:      r.AssignmentLeadID,
		AdjusterID:            r.AdjusterID,
		ClaimNumber:           r.ClaimNumber,
		InsuredName:           r.InsuredName,
		AssignmentName:        r.AssignmentName,
		DateOfLoss:            r.DateOfLoss,
		PrimaryContactID:      r.PrimaryContactID,
		SecondaryContactID:    r.SecondaryContactID,
		LossLocationID:        r.LossLocationID,
		ClaimDetail:           r.ClaimDetail,
		AssignmentDescription: r.AssignmentDescription,
		Carriers:              cars,
	}

	// Note: CategoryData will be handled separately after assignment creation
	// since it requires the assignment ID
	return assignment
}

type UpdateAssignmentRequest struct {
	Status                string                      `json:"status"`
	AssignmentLeadID      uint                        `json:"assignmentLeadId"`
	AdjusterID            uint                        `json:"adjusterId"`
	Carriers              []uint                      `json:"carriers"`
	CategoryData          []AssignmentCategoryRequest `json:"categoryData"`
	ClaimNumber           string                      `json:"claimNumber"`
	InsuredName           string                      `json:"insuredName"`
	AssignmentName        string                      `json:"assignmentName"`
	DateOfLoss            time.Time                   `json:"dateOfLoss"`
	PrimaryContactID      uint                        `json:"primaryContactId"`
	SecondaryContactID    *uint                       `json:"secondaryContactId"`
	LossLocationID        uint                        `json:"lossLocationId"`
	ClaimDetail           *string                     `json:"claimDetail"`
	AssignmentDescription *string                     `json:"assignmentDescription"`
}

func ToUpdateDomainAssignment(r *UpdateAssignmentRequest, assignmentID uint) *domain.Assignment {
	var cars []*domain.Carrier
	for _, carrID := range r.Carriers {
		cars = append(cars, &domain.Carrier{ID: carrID})
	}

	assignment := &domain.Assignment{
		ID:                    assignmentID,
		Status:                domain.AssignmentStatus(r.Status),
		AssignmentLeadID:      r.AssignmentLeadID,
		AdjusterID:            r.AdjusterID,
		ClaimNumber:           r.ClaimNumber,
		InsuredName:           r.InsuredName,
		AssignmentName:        r.AssignmentName,
		DateOfLoss:            r.DateOfLoss,
		PrimaryContactID:      r.PrimaryContactID,
		SecondaryContactID:    r.SecondaryContactID,
		LossLocationID:        r.LossLocationID,
		ClaimDetail:           r.ClaimDetail,
		AssignmentDescription: r.AssignmentDescription,
		Carriers:              cars,
	}

	// Note: CategoryData will be handled separately during update
	// since it requires specific handling for the many-to-many relationship
	return assignment
}

// Helper function to convert CategoryData from request to domain
func ToDomainAssignmentCategories(categoryData []AssignmentCategoryRequest, assignmentID uint) []domain.AssignmentCategory {
	var result []domain.AssignmentCategory
	for _, catData := range categoryData {
		result = append(result, domain.AssignmentCategory{
			AssignmentID:    assignmentID,
			CategoryID:      catData.CategoryID,
			PresentValue:    catData.PresentValue,
			AsAnalyzedValue: catData.AsAnalyzedValue,
		})
	}
	return result
}
