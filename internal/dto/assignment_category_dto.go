package dto

import (
	"fmt"
	"time"

	"github.com/SneatX/phillips_go/internal/core/domain"
)

type AssignmentCategoryResponse struct {
	AssignmentID    uint             `json:"assignmentId"`
	CategoryID      uint             `json:"categoryId"`
	PresentValue    float64          `json:"presentValue"`
	AsAnalyzedValue float64          `json:"asAnalyzedValue"`
	Category        CategoryResponse `json:"category"`
	CreatedAt       time.Time        `json:"createdAt"`
	UpdatedAt       time.Time        `json:"updatedAt"`
}

func FromDomainAssignmentCategory(assignmentCategory domain.AssignmentCategory) AssignmentCategoryResponse {
	return AssignmentCategoryResponse{
		AssignmentID:    assignmentCategory.AssignmentID,
		CategoryID:      assignmentCategory.CategoryID,
		PresentValue:    assignmentCategory.PresentValue,
		AsAnalyzedValue: assignmentCategory.AsAnalyzedValue,
		Category:        FromDomainCategory(assignmentCategory.Category),
		CreatedAt:       assignmentCategory.CreatedAt,
		UpdatedAt:       assignmentCategory.UpdatedAt,
	}
}

type CreateAssignmentCategoryRequest struct {
	CategoryID      uint     `json:"categoryId" binding:"required"`
	PresentValue    *float64 `json:"presentValue"`
	AsAnalyzedValue *float64 `json:"asAnalyzedValue"`
}

func ToDomainAssignmentCategory(r *CreateAssignmentCategoryRequest) *domain.AssignmentCategory {
	return &domain.AssignmentCategory{
		CategoryID:      r.CategoryID,
		PresentValue:    *r.PresentValue,
		AsAnalyzedValue: *r.AsAnalyzedValue,
	}
}

// Validate validates the CreateAssignmentCategoryRequest
func (r *CreateAssignmentCategoryRequest) Validate() error {
	if r.PresentValue == nil {
		return fmt.Errorf("presentValue is required")
	}
	if r.AsAnalyzedValue == nil {
		return fmt.Errorf("asAnalyzedValue is required")
	}
	if *r.PresentValue < 0 {
		return fmt.Errorf("presentValue cannot be negative")
	}
	if *r.AsAnalyzedValue < 0 {
		return fmt.Errorf("asAnalyzedValue cannot be negative")
	}
	return nil
}
