package postgres

import (
	"errors"
	"fmt"

	"github.com/SneatX/phillips_go/internal/core/domain"
	appErrors "github.com/SneatX/phillips_go/internal/shared/errors"
	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

type assignmentRepository struct {
	DB *gorm.DB
}

func NewAssignmentRepository(DB *gorm.DB) *assignmentRepository {
	return &assignmentRepository{DB: DB}
}

func (r *assignmentRepository) GetAll() ([]*domain.Assignment, error) {
	var assignments []*domain.Assignment
	if err := r.DB.
		Preload("CreatedBy").
		Preload("AssignmentLead").
		Preload("Adjuster").
		Preload("Adjuster.Carriers").
		Preload("Carriers").
		Preload("CategoryData").
		Preload("CategoryData.Category").
		Preload("PrimaryContact").
		Preload("SecondaryContact").
		Preload("LossLocation").
		Find(&assignments).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to retrieve all assignments from database", err)
	}
	return assignments, nil
}

func (r *assignmentRepository) GetByUserId(userID uint) ([]*domain.Assignment, error) {
	var assignments []*domain.Assignment

	if err := r.DB.
		Preload("CreatedBy").
		Preload("AssignmentLead").
		Preload("Adjuster").
		Preload("Adjuster.Carriers").
		Preload("Carriers").
		Preload("CategoryData").
		Preload("CategoryData.Category").
		Preload("PrimaryContact").
		Preload("SecondaryContact").
		Preload("LossLocation").
		Where("created_by_id = ?", userID).Find(&assignments).Error; err != nil {
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to retrieve assignments from database with user id: %d", userID), err)
	}

	return assignments, nil
}

func (r *assignmentRepository) GetByID(id uint) (*domain.Assignment, error) {
	var assignment domain.Assignment

	if err := r.DB.
		Preload("CreatedBy").
		Preload("AssignmentLead").
		Preload("Adjuster").
		Preload("Adjuster.Carriers").
		Preload("Carriers").
		Preload("CategoryData").
		Preload("CategoryData.Category").
		Preload("PrimaryContact").
		Preload("SecondaryContact").
		Preload("LossLocation").
		First(&assignment, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErrors.NewNotFoundError(fmt.Sprintf("assignment with ID %d could not be found", id), err)
		}
		return nil, appErrors.NewInternalError(fmt.Sprintf("failed to retrieve assignment %d due to unexpected issue", id), err)
	}

	return &assignment, nil
}

func (r *assignmentRepository) Create(assignment *domain.Assignment) (*domain.Assignment, error) {
	// Start a transaction to ensure data consistency
	tx := r.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Store CategoryData temporarily and clear it from assignment for initial creation
	categoryData := assignment.CategoryData
	assignment.CategoryData = nil

	// Create the assignment first
	if result := tx.Create(assignment); result.Error != nil {
		tx.Rollback()
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError("assignment with this claim number already exists")
			}
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that all related entities exist before creating an assignment")
			}
		}
		return nil, appErrors.NewInternalError("failed to create assignment", result.Error)
	}

	// Now create CategoryData with the assignment ID
	if err := r.createCategoryData(tx, assignment.ID, categoryData); err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return nil, appErrors.NewInternalError("failed to commit assignment creation", err)
	}

	// Reload the assignment with all preloaded data
	if err := r.DB.
		Preload("CategoryData").
		Preload("CategoryData.Category").
		Preload("Carriers").
		First(&assignment, assignment.ID).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to load created assignment", err)
	}

	return assignment, nil
}

func (r *assignmentRepository) Update(assignment *domain.Assignment) (*domain.Assignment, error) {
	// Start a transaction to ensure data consistency
	tx := r.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var existing domain.Assignment
	if err := tx.First(&existing, assignment.ID).Error; err != nil {
		tx.Rollback()
		return nil, appErrors.NewNotFoundError("assignment not found")
	}

	// Handle Carriers update
	var newCarriers []*domain.Carrier
	if len(assignment.Carriers) > 0 {
		carrierIDs := make([]uint, len(assignment.Carriers))
		for i, carrier := range assignment.Carriers {
			carrierIDs[i] = carrier.ID
		}
		if err := tx.Where("id IN ?", carrierIDs).Find(&newCarriers).Error; err != nil {
			tx.Rollback()
			return nil, appErrors.NewInternalError("failed to find carriers", err)
		}
	}

	if err := tx.Model(&existing).Association("Carriers").Replace(&newCarriers); err != nil {
		tx.Rollback()
		return nil, appErrors.NewInternalError("failed to update assignment carriers", err)
	}

	// Handle CategoryData update - replace all existing with new ones
	if err := r.replaceCategoryData(tx, assignment.ID, assignment.CategoryData); err != nil {
		tx.Rollback()
		return nil, err
	}

	// Clear CategoryData from assignment for main update (already handled above)
	assignment.CategoryData = nil

	// Update the main assignment fields
	if result := tx.Model(&existing).Updates(assignment); result.Error != nil {
		tx.Rollback()
		var pgErr *pgconn.PgError
		if errors.As(result.Error, &pgErr) {
			fmt.Printf("PostgreSQL error code: %s, message: %s\n", pgErr.Code, pgErr.Message)
			if pgErr.Code == "23505" {
				return nil, appErrors.NewConflictError("assignment with this claim number already exists")
			}
			if pgErr.Code == "23503" {
				return nil, appErrors.NewNotFoundError("foreign key constraint violation, please check that all related entities exist before updating an assignment")
			}
		}
		return nil, appErrors.NewInternalError("failed to update assignment", result.Error)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return nil, appErrors.NewInternalError("failed to commit assignment update", err)
	}

	// Reload the assignment with all preloaded data
	if err := r.DB.
		Preload("CategoryData").
		Preload("CategoryData.Category").
		Preload("Carriers").
		First(&assignment, assignment.ID).Error; err != nil {
		return nil, appErrors.NewInternalError("failed to load updated assignment", err)
	}

	return assignment, nil
}

// Helper methods for CategoryData management

// createCategoryData creates new AssignmentCategory records for the given assignment
func (r *assignmentRepository) createCategoryData(tx *gorm.DB, assignmentID uint, categoryData []*domain.AssignmentCategory) error {
	if len(categoryData) == 0 {
		return nil
	}

	for _, catData := range categoryData {
		catData.AssignmentID = assignmentID
	}

	if err := tx.Create(&categoryData).Error; err != nil {
		return appErrors.NewInternalError("failed to create assignment category data", err)
	}

	return nil
}

// deleteCategoryData removes all AssignmentCategory records for the given assignment
func (r *assignmentRepository) deleteCategoryData(tx *gorm.DB, assignmentID uint) error {
	if err := tx.Where("assignment_id = ?", assignmentID).Delete(&domain.AssignmentCategory{}).Error; err != nil {
		return appErrors.NewInternalError("failed to delete existing category data", err)
	}
	return nil
}

// replaceCategoryData replaces all CategoryData for an assignment
func (r *assignmentRepository) replaceCategoryData(tx *gorm.DB, assignmentID uint, newCategoryData []*domain.AssignmentCategory) error {
	// Delete existing CategoryData
	if err := r.deleteCategoryData(tx, assignmentID); err != nil {
		return err
	}

	// Create new CategoryData
	if err := r.createCategoryData(tx, assignmentID, newCategoryData); err != nil {
		return err
	}

	return nil
}
